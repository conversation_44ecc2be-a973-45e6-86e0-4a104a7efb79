'use client';

import React, { useEffect } from 'react';
import { FiFilter, FiDownload, FiMoreVertical } from 'react-icons/fi';
import { IoIosSearch } from 'react-icons/io';
import { RxCounterClockwiseClock } from 'react-icons/rx';
import Link from 'next/link';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

const members = [
  {
    clientname: 'TechNova',
    date: '11-01-2025',
    passenger: '10',
    status: 'Active',
    plan: 'Basic',
  },
  {
    clientname: 'InnovaTech',
    date: '11-01-2025',
    passenger: '10',
    status: 'Active',
    plan: 'Basic',
  },
];
const circleColors = [
  { bg: '#FFEED4', text: '#CC7A00' }, // darker orange
  { bg: '#D6FFEE', text: '#008B5B' }, // darker green
  { bg: '#FBE9FF', text: '#800080' }, // darker purple
];
export default function Clients() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Clients List');
  }, []);
  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-end">
        <button className="rounded-full bg-[#3707EF] px-4 py-2 text-sm text-white transition hover:bg-[#4338ca]">
          + Add New Client
        </button>
      </div>

      <div className="overflow-x-auto rounded-lg border bg-white">
        <div className="header-bar bg-table-head flex items-center justify-between rounded-t-lg px-3 py-1">
          <form className="max-w-md flex-1">
            <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-[#76787A]">
                <IoIosSearch size={20} />
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 rounded-full border border-0 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              />
            </div>
          </form>

          <div className="flex items-center gap-2">
            <div
              className="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <FiFilter size={18} />
                Filters
              </button>
            </div>

            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <RxCounterClockwiseClock size={22} />
            </button>

            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <FiDownload size={18} />
            </button>
          </div>
        </div>
        <table className="min-w-full divide-y divide-gray-200 text-[12px]">
          <thead>
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              {[
                'Client Name',
                'Date Added',
                'No of Passengers',
                'Status',
                'Subscription Plan',
                '',
              ].map(header => (
                <th
                  key={header}
                  className="px-4 py-3 text-left font-medium whitespace-nowrap text-[#76787A]"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {members.map((member, idx) => (
              <tr
                key={idx}
                className="cursor-pointer transition hover:bg-[#E4FFF4]"
              >
                <td className="px-4 py-3 text-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </td>
                <td className="flex items-center gap-2 px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {/* Circle with initials */}
                  <div
                    className="flex h-7 w-7 items-center justify-center rounded-full text-[10px] font-semibold"
                    style={{
                      backgroundColor:
                        circleColors[idx % circleColors.length].bg,
                      color: circleColors[idx % circleColors.length].text,
                    }}
                  >
                    {member.clientname
                      .replace(/([a-z])([A-Z])/g, '$1 $2') // insert space before capital letters
                      .split(' ')
                      .map(word => word[0])
                      .join('')
                      .toUpperCase()}
                  </div>

                  {/* Client Name */}
                  {member.clientname}
                </td>

                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.date}
                </td>
                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.passenger}
                </td>
                <td
                  className={`flex items-center gap-1 px-4 py-3 text-[12px] font-normal whitespace-nowrap ${
                    member.status === 'Active'
                      ? 'text-[#13BB76]' // green
                      : member.status === 'Expired'
                      ? 'text-[#FF0000]' // red (you can change to any color you want)
                      : ''
                  }`}
                >
                  <span
                    className={`inline-block h-2 w-2 rounded-full ${
                      member.status === 'Active'
                        ? 'bg-[#13BB76]'
                        : member.status === 'Expired'
                        ? 'bg-[#FF0000]'
                        : ''
                    }`}
                  ></span>
                  {member.status}
                </td>
                <td className="px-4 py-3 text-[12px] font-normal whitespace-nowrap text-[#050013]">
                  {member.plan}
                </td>
                <td className="cursor-pointer px-4 py-3 text-[16px] text-[#76787A] select-none">
                  <FiMoreVertical />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
