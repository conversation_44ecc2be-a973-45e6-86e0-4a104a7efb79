'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Dashboard() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Admin Dashboard');
  }, []);
  const accessSections = [
    {
      title: 'User & Role Management',
      items: [
        'Create, edit, delete Admins, Dispatchers, Drivers, Users',
        'Assign Admins to taxi companies',
        'Manage permissions for all roles',
      ],
    },
    {
      title: 'Booking & Dispatch Control',
      items: [
        'View, edit, and manage all bookings',
        'Assign/reassign drivers to rides',
        'View driver live locations',
      ],
    },
    {
      title: 'Company & Financial Management',
      items: [
        'Add, edit, or remove taxi companies',
        'Manage company subscription plans',
        'View & modify company-specific pricing and fare structures',
      ],
    },
    {
      title: 'System & Policy Settings',
      items: [
        'Define platform-wide fare policies',
        'Set geofencing rules & restrictions',
        'Control global discount and promo policies',
      ],
    },
    {
      title: 'Reporting & Analytics',
      items: [
        'View and export reports on revenue, ride activity, and system performance',
        'Monitor driver performance & customer ratings',
        'Analyze dispatcher efficiency',
      ],
    },
  ];

  const userDetails = {
    name: 'Silverline Solutions',
    regestration: '*********',
    tax: '*********',
    businesstype: 'LLC',
    url: 'www.silverline.com',
  };

  return (
    <div>
      {/* Overview Section */}
      <div className="p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card
            subheading="Admins"
            value="04"
            link={
              <Link
                href={`/admin/admins`}
                className="text-[#3324E3] hover:underline"
              >
                View All
              </Link>
            }
            icon={
              <img
                src="/images/admins.svg"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
          />

          <Card
            subheading="Dispatchers"
            value="02"
            link={
              <Link
                href={`/admin/dispatcher`}
                className="text-[#3324E3] hover:underline"
              >
                View All
              </Link>
            }
            icon={
              <img
                src="/images/dispatcher.svg"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
          />

          <Card
            icon={
              <img
                src="/images/vehicle.svg"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
            bg="bg-blue-50"
            value="17"
            subheading="Vehicles"
            // link="View All"
            link={
              <Link
                href={`/admin/vehicles`}
                className="text-[#3324E3] hover:underline"
              >
                View All
              </Link>
            }
          />

          <Card
            icon={
              <img
                src="/images/driver.svg"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
            bg="bg-yellow-50"
            value="17"
            subheading="Drivers"
            link={
              <Link
                href={`/admin/drivers`}
                className="text-[#3324E3] hover:underline"
              >
                View All
              </Link>
            }
          />

          <Card
            icon={
              <img
                src="/images/client.svg"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
            subheading="Clients"
            bg="bg-red-50"
            value="30"
            link={
              <Link
                href={`/admin/clients`}
                className="text-[#3324E3] hover:underline"
              >
                View All
              </Link>
            }
          />

          <Card
            icon={
              <img
                src="/images/passanger.png"
                alt="Icon"
                className="h-15 w-15 object-contain"
              />
            }
            subheading="Passenger"
            bg="bg-purple-50"
            value="750"
            link="View All"
          />
        </div>
      </div>
    </div>
  );
}

// Card Component
function Card({ icon, bg, value, subtext, link, subheading }) {
  return (
    <div className="flex flex-col justify-between rounded-lg border border-gray-200 bg-white">
      <div className="flex items-center justify-between px-6 py-3">
        <div className="space-y-1">
          {subheading && (
            <p className="text-[13px] font-normal text-[#76787A]">
              {subheading}
            </p>
          )}
          <p className="text-[24px] font-semibold text-[#050013]">{value}</p>
          {subtext && <p className="text-[10px] text-[#76787A]">{subtext}</p>}
        </div>
        <div
          className={`h-15 w-15 rounded-full ${bg} flex items-center justify-center text-lg`}
        >
          {icon}
        </div>
      </div>
      <div className="rounded-b-lg bg-[#F6F8FB] px-6 py-4">
        <button className="text-left text-[13px] text-[#3324E3] underline">
          {link}
        </button>
      </div>
    </div>
  );
}
