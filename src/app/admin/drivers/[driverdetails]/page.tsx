'use client';

import React, { useEffect } from 'react';
import DriverDetails from '../components/Driverdetails';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Page() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle('Driver Details');
  }, []);
  return (
    <div className="px-[45px] py-[31px]">
      <DriverDetails />
    </div>
  );
}
