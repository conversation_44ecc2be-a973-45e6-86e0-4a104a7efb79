'use client';
import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import dayjs from 'dayjs';
import { Fragment, useState } from 'react';
import { toast } from 'react-toastify';
import { Dialog, Transition } from '@headlessui/react';
import { useMutation } from '@tanstack/react-query';
import { editDriverDetails } from '../state/queries';
import { queryClient } from '@/hooks/useGlobalContext';
import Tippy from '@tippyjs/react';
import { DownloadIcon } from '@/icons';

type DeleteModalprops = {
  isOpen: boolean;
  id: string;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleDeleteDriver?: (id: string) => void;
  title?: string;
  message?: string;
};

interface Document {
  id: number;
  documentName: string;
  dateTime: string;
  uploadedBy: string;
  lastModified: string;
  expiryDate: string;
  status: 'Verified' | 'Pending' | 'Rejected' | 'Expired';
  comments: string;
}

type SortField =
  | 'documentName'
  | 'dateTime'
  | 'uploadedBy'
  | 'expiryDate'
  | 'status';
type SortDirection = 'asc' | 'desc';

interface DocumentTabProps {
  driverDetails: any;
}

function DeleteModal(props: DeleteModalprops) {
  const { isOpen, setIsOpen, handleDeleteDriver, id, title, message } = props;

  const closeModal = () => setIsOpen(false);

  const handleDelete = () => {
    if (handleDeleteDriver) {
      handleDeleteDriver(id);
    }
    setIsOpen(false); // close after delete
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-999" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="modal-bg-black fixed inset-0 opacity-[40%]" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg leading-6 font-medium text-[#050013]"
                >
                  {title}
                </Dialog.Title>
                <div className="mt-2">
                  <p className="text-sm text-[#76787A]">{message}</p>
                </div>

                <div className="mt-4 flex items-center justify-end">
                  <button
                    type="button"
                    className="btn-border me-[10px] mb-2 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2.5 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none"
                    onClick={closeModal}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none"
                    onClick={handleDelete}
                  >
                    Delete
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

export default function DocumentTab({ driverDetails }: DocumentTabProps) {
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [selectedDocId, setSelectedDocId] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  console.log(driverDetails);

  const tableData: Document[] = [
    {
      id: 1,
      documentName: 'Driving Licence',
      dateTime: dayjs(driverDetails?.createdAt).format('DD-MM-YYYY HH:mm'),
      uploadedBy: 'Admin',
      lastModified: 'N/A',
      expiryDate: 'N/A',
      status: !driverDetails?.drivingLicenceVerification
        ? 'Verified'
        : 'Pending',
      comments: !driverDetails?.drivingLicenceVerification
        ? 'Licence verified'
        : 'Awaiting verification',
    },
    {
      id: 3,
      documentName: 'Insurance',
      dateTime: dayjs(driverDetails?.createdAt).format('DD-MM-YYYY HH:mm'),
      uploadedBy: 'Admin',
      lastModified: 'N/A',
      expiryDate: driverDetails?.insuranceExpiryDate
        ? dayjs(driverDetails.insuranceExpiryDate).format('DD-MM-YYYY')
        : 'N/A',
      status: !driverDetails?.insuranceVerification ? 'Verified' : 'Pending',
      comments: !driverDetails?.insuranceVerification
        ? 'Insurance verified'
        : 'Awaiting verification',
    },
  ];

  const getDocumentNameById = (id: number): string | undefined => {
    const document = tableData.find(doc => doc.id === id);
    return document?.documentName;
  };

  const editDriverDriverMutation = useMutation({
    mutationFn: async (data: FormData) => {
      return editDriverDetails(data, driverDetails.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverDetails'] });
      toast.success('Document deleted successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });

  const handleDeleteDocument = (id: string) => {
    setShowDeleteModal(false);
    setSelectedDocId(null);

    const name = getDocumentNameById(1);

    if (name == 'Driving Licence') {
    }

    if (name == 'Insurance') {
    }

    editDriverDriverMutation.mutate(payLoad);
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field is clicked
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedData = [...tableData].sort((a, b) => {
    if (!sortField) return 0;

    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue === bValue) return 0;

    const comparison = aValue > bValue ? 1 : -1;
    return sortDirection === 'asc' ? comparison : -comparison;
  });

  const getSortIndicator = (field: SortField) => {
    if (sortField !== field) return '⇅';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="tabled">
      <div className="overflow-hidden rounded-b-[12px] border-t-0 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="header-bar bg-tables flex items-center justify-between p-3 dark:bg-gray-800">
          {/* Search Bar */}
          <form className="max-w-md flex-1">
            <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Search
            </label>
          </form>
          {/* Buttons Container */}
          <div className="flex items-center gap-2">
            {/* Filter Button */}
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                />
              </svg>
              Filters
            </button>
            {/* Refresh Button */}
            <button
              type="button"
              aria-label="refresh"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <PiClockCounterClockwiseLight size={22} />
            </button>
            {/* Download Button */}
            <button
              type="button"
              aria-label="download"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <DownloadIcon />
            </button>
          </div>
        </div>
        <div className="custom-scrollbar max-w-full overflow-x-auto">
          <div className="max-w-[900px] min-w-[-webkit-fill-available]">
            <Table>
              {/* Table Header */}
              <TableHeader className="bg-tables border-b border-gray-100 dark:border-white/[0.05]">
                <TableRow className="border-t border-b">
                  <TableCell isHeader className="px-5 py-2 text-start sm:px-6">
                    <input
                      type="checkbox"
                      placeholder="Select Data"
                      name="selectOrder"
                      className="form-checkbox h-4 w-4 rounded text-blue-600"
                    />
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <button
                      onClick={() => handleSort('documentName')}
                      className="flex items-center"
                    >
                      Document Name
                      <span className="px-1 text-[11px]">
                        {getSortIndicator('documentName')}
                      </span>
                    </button>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <button
                      onClick={() => handleSort('dateTime')}
                      className="flex items-center"
                    >
                      Date/Time
                      <span className="px-1 text-[11px]">
                        {getSortIndicator('dateTime')}
                      </span>
                    </button>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <button
                      onClick={() => handleSort('uploadedBy')}
                      className="flex items-center"
                    >
                      Uploaded By
                      <span className="px-1 text-[11px]">
                        {getSortIndicator('uploadedBy')}
                      </span>
                    </button>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    Last Modified
                  </TableCell>

                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <button
                      onClick={() => handleSort('expiryDate')}
                      className="flex items-center"
                    >
                      Expiry Date
                      <span className="px-1 text-[11px]">
                        {getSortIndicator('expiryDate')}
                      </span>
                    </button>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <button
                      onClick={() => handleSort('status')}
                      className="flex items-center"
                    >
                      Status
                      <span className="px-1 text-[11px]">
                        {getSortIndicator('status')}
                      </span>
                    </button>
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    Comments
                  </TableCell>
                  <TableCell
                    isHeader
                    className="text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    &nbsp;
                  </TableCell>
                </TableRow>
              </TableHeader>
              {/* Table Body */}
              <TableBody className="bg-tables divide-y border-b dark:divide-white/[0.05]">
                {sortedData.map((order, index) => (
                  <TableRow
                    key={index}
                    className="bg-tables cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <TableCell className="px-5 py-2 text-start sm:px-6">
                      <input
                        type="checkbox"
                        placeholder="Select Data"
                        name="selectOrder"
                        value={order.id}
                        className="form-checkbox h-4 w-4 rounded text-blue-600"
                      />
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.documentName}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.dateTime}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.uploadedBy}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.lastModified}{' '}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.expiryDate}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p
                        className={`${
                          order?.status === 'Verified'
                            ? 'text-[#00CA77]'
                            : 'text-[#FF7C00]'
                        } flex items-center gap-1 text-[11px]`}
                      >
                        <span
                          className={`block h-[6px] w-[6px] rounded-full ${
                            order?.status === 'Verified'
                              ? 'bg-[#00CA77]'
                              : 'bg-[#FF7C00]'
                          }`}
                        ></span>
                        {order?.status ? 'Verified' : 'Pending'}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="flex items-center gap-1 text-[12px] text-[#050013]">
                        {order?.comments}
                      </p>
                    </TableCell>
                    <TableCell className="px-4">
                      <Tippy
                        content={
                          <div className="bg-white text-gray-900">
                            <div className="flex flex-col space-y-1 p-1">
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                View Document
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Approve Document
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Request Update
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Replace Document
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                {' '}
                                Revoke Verification
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Add Comment
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                View History
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Set Expiry Date
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Delete Document
                              </button>

                              <button
                                onClick={() => {
                                  setSelectedDocId(String(order.id));
                                  setShowDeleteModal(true);
                                }}
                                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                              >
                                Delete Document
                              </button>
                              <DeleteModal
                                id={selectedDocId}
                                isOpen={showDeleteModal}
                                setIsOpen={setShowDeleteModal}
                                handleDeleteDriver={handleDeleteDocument}
                                title="Delete Document"
                                message="Are you sure you want to delete this document? This cannot be undone."
                              />
                            </div>
                          </div>
                        }
                        interactive={true}
                        placement="right"
                        theme="light"
                        arrow={false}
                        duration={0}
                        className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                      >
                        <button
                          type="button"
                          className="focus:outline-none"
                          aria-label="clock"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            className="size-6"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </Tippy>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}
