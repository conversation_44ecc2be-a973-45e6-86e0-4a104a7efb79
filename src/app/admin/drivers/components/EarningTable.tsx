'use client';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import {
  PiClockCounterClockwiseLight,
  PiDotsThreeVerticalBold,
} from 'react-icons/pi';
import { LuListFilter } from 'react-icons/lu';
import { FaDownload, FaSearch } from 'react-icons/fa';
import { useEffect, useMemo, useState } from 'react';
import { fetchDriverEarnings } from '../state/queries';
import { useQuery } from '@tanstack/react-query';
import { IoIosSearch } from 'react-icons/io';
import Badge from '@/components/ui/badge/Badge';

interface Trip {
  tripId: number;
  dateTime: string;
  baseFare: number;
  bonuses: number;
  penalties: number;
  tips: number;
  totalFare: number;
  paymentType: string;
  companyEarnings: number;
  driverEarnings: number;
  paymentStatus: string;
  payoutDate: string;
}

export default function DriverEarningsTable({
  driverDetails,
}: {
  driverDetails: any;
}) {
  const [loading, setLoading] = useState(true);
  const [selectedTrips, setSelectedTrips] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  const {
    data: trips,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['driverEarnings', driverDetails?.id],
    queryFn: () => {
      if (!driverDetails?.id) {
        return Promise.reject(new Error('Driver ID is missing'));
      }
      return fetchDriverEarnings(driverDetails.id.toString());
    },
    enabled: !!driverDetails?.id,
  });

  // const filteredTrips = trips.filter(trip => {
  //     const searchLower = searchTerm.toLowerCase();
  //     return (
  //         String(trip.tripId).includes(searchLower) ||
  //         trip.dateTime.toLowerCase().includes(searchLower) ||
  //         trip.paymentType.toLowerCase().includes(searchLower) ||
  //         trip.paymentStatus.toLowerCase().includes(searchLower)
  //     );
  // });

  const handleSort = (key: string) => {
    setSortConfig(prev =>
      prev?.key === key
        ? { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' }
        : { key, direction: 'asc' }
    );
  };

  const getSortIcon = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) return '⇅';
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const filteredTrips = useMemo(() => {
    if (!sortConfig) return trips;

    const sorted = [...trips].sort((a, b) => {
      const valA = a[sortConfig.key];
      const valB = b[sortConfig.key];

      if (valA == null) return 1;
      if (valB == null) return -1;

      if (['created_at', 'resolution_date'].includes(sortConfig.key)) {
        return sortConfig.direction === 'asc'
          ? new Date(valA).getTime() - new Date(valB).getTime()
          : new Date(valB).getTime() - new Date(valA).getTime();
      }

      // If sorting by string
      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortConfig.direction === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }

      // Fallback
      return sortConfig.direction === 'asc'
        ? (valA as any) > (valB as any)
          ? 1
          : -1
        : (valA as any) < (valB as any)
        ? 1
        : -1;
    });

    return sorted;
  }, [trips, sortConfig]);

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (value: number, total: number) => {
    return `${((value / total) * 100).toFixed(0)}%`;
  };

  const handleSelectTrip = (tripId: number) => {
    setSelectedTrips(prev =>
      prev.includes(tripId)
        ? prev.filter(id => id !== tripId)
        : [...prev, tripId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTrips.length === filteredTrips.length) {
      setSelectedTrips([]);
    } else {
      setSelectedTrips(filteredTrips.map(trip => trip.tripId));
    }
  };

  return (
    <div className="p-4 text-blue-500">
      No earnings data found for this driver.
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex h-64 flex-col items-center justify-center text-gray-500">
        <svg
          className="mb-2 h-5 w-5 animate-spin text-blue-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v8H4z"
          />
        </svg>
        <span>Loading trip earnings...</span>
      </div>
    );
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error.message}</div>;
  }

  if (trips?.length === 0) {
    return (
      <div className="p-4 text-blue-500">
        No earnings data found for this driver.
      </div>
    );
  }

  return (
    <div className="tabled">
      <div className="overflow-hidden rounded-b-[12px] border-t-0 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="header-bar bg-tables flex items-center justify-between border-b px-3 py-2 dark:bg-gray-800">
          <div className="flex items-center space-x-4">
            {/* <div className="relative">
                            <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                <FaSearch className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                            </div>
                            <input
                                type="search"
                                className="flex items-center gap-2 py-2 px-5 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                placeholder="Search trips..."
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div> */}
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                <IoIosSearch className="h-[20px] w-[20px] text-[#050013]" />
              </div>
              <input
                type="search"
                id="default-search"
                className="block w-full rounded-full border border-0 border-gray-300 border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                placeholder="Search here"
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
            {/* <span className="text-sm text-gray-700">
                            {selectedTrips.length} selected
                        </span> */}
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <LuListFilter />
              Trip Details
            </button>
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <LuListFilter />
              Payment Status
            </button>
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <LuListFilter />
              Earnings
            </button>
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <LuListFilter />
              Select Date
            </button>
            <button
              type="button"
              className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                />
              </svg>
              {/* <FaFilter className="w-4 h-4" /> */}
              Filters
            </button>
            <button
              type="button"
              aria-label="refresh"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <PiClockCounterClockwiseLight size={22} />
            </button>
            <button
              type="button"
              aria-label="download"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="size-4"
              >
                <path
                  fillRule="evenodd"
                  d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                  clip-rule="evenodd"
                />
              </svg>
              {/* <FaDownload size={20} /> */}
            </button>
            <button
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              aria-label="actions"
            >
              <PiDotsThreeVerticalBold size={22} />
            </button>
          </div>
        </div>
        <div className="custom-scrollbar max-w-full overflow-x-auto">
          {/* <div className="min-w-[1102px]"> */}
          <div className="max-w-[800px] min-w-[-webkit-fill-available]">
            <Table>
              <TableHeader className="bg-tables border-b dark:border-white/[0.05]">
                <TableRow>
                  <TableCell
                    isHeader
                    className="w-10 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-400"
                  >
                    <input
                      type="checkbox"
                      checked={
                        (selectedTrips?.length === filteredTrips?.length &&
                          filteredTrips?.length > 0) ||
                        false
                      }
                      onChange={handleSelectAll}
                      className="form-checkbox h-4 w-4 rounded text-blue-600"
                    />
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('tripId')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('tripId');
                      }}
                    >
                      Trip ID{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('tripId')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('dateTime')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('dateTime');
                      }}
                    >
                      Date/Time{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('dateTime')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('baseFare')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('baseFare');
                      }}
                    >
                      Base Fare{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('baseFare')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('bonuses')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('bonuses');
                      }}
                    >
                      Bonuses{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('bonuses')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('penalties')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('penalties');
                      }}
                    >
                      Penalties{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('penalties')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('tips')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('tips');
                      }}
                    >
                      Tips{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('tips')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('totalFare')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('totalFare');
                      }}
                    >
                      Total Fare{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('totalFare')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('paymentType')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('paymentType');
                      }}
                    >
                      Payment Type{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('paymentType')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('companyEarnings')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('companyEarnings');
                      }}
                    >
                      Company Earnings{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('companyEarnings')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('driverEarnings')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('driverEarnings');
                      }}
                    >
                      Driver Earnings{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('driverEarnings')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('paymentStatus')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('paymentStatus');
                      }}
                    >
                      Payment Status{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('paymentStatus')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  >
                    <span
                      className="cursor-pointer select-none"
                      onClick={() => handleSort('payoutDate')}
                      role="button"
                      tabIndex={0}
                      onKeyPress={e => {
                        if (e.key === 'Enter' || e.key === ' ')
                          handleSort('payoutDate');
                      }}
                    >
                      Payout Date{' '}
                      <span className="px-1 text-[11px]">
                        {getSortIcon('payoutDate')}
                      </span>
                    </span>
                  </TableCell>

                  <TableCell
                    isHeader
                    className="px-4 py-3 text-start text-xs font-medium whitespace-nowrap text-gray-500 dark:text-gray-400"
                  ></TableCell>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-tables divide-y dark:divide-white/[0.05]">
                {filteredTrips?.map(trip => (
                  <TableRow
                    key={trip.tripId}
                    className="whitespace-nowrap dark:hover:bg-gray-800"
                  >
                    <TableCell className="w-10 px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedTrips.includes(trip.tripId)}
                        onChange={() => handleSelectTrip(trip.tripId)}
                        className="form-checkbox h-4 w-4 rounded text-blue-600"
                      />
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        #{String(trip.tripId).padStart(5, '0')}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {/* {formatDateTime(trip.dateTime)} */}
                        15-01-2025 11:27
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {formatCurrency(trip.baseFare)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {formatCurrency(trip.bonuses)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {formatCurrency(trip.penalties)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {formatCurrency(trip.tips)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs font-medium text-[#050013]">
                        {formatCurrency(trip.totalFare)}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {trip.paymentType}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {trip.companyEarnings}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3 whitespace-nowrap">
                      <p className="text-xs text-[#050013]">
                        {trip.driverEarnings}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <Badge
                        size="sm"
                        color={
                          trip.paymentStatus === 'Completed'
                            ? 'success'
                            : trip.paymentStatus === 'Pending'
                            ? 'warning'
                            : 'error'
                        }
                      >
                        {trip.paymentStatus}
                      </Badge>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <p className="text-xs text-[#050013]">
                        {trip.payoutDate
                          ? formatDateTime(trip.payoutDate)
                          : 'N/A'}
                      </p>
                    </TableCell>
                    <TableCell className="px-4 py-3">
                      <Tippy
                        content={
                          <div className="bg-white text-gray-900">
                            <div className="flex flex-col space-y-1 p-1">
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Approve Pending Payments
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Bulk Adjust Payments
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Resolve Payment
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Mark all as paid
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Report Payment Discrepancies
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Generate Payment Summary
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Download Receipts
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Report All Issues
                              </button>
                              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                Notify Driver
                              </button>
                            </div>
                          </div>
                        }
                        interactive={true}
                        placement="left"
                        theme="light"
                        arrow={false}
                        duration={0}
                        className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                      >
                        <button
                          type="button"
                          className="focus:outline-none"
                          aria-label="actions"
                        >
                          <PiDotsThreeVerticalBold size={20} />
                        </button>
                      </Tippy>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}
