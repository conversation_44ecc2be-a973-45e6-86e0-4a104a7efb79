'use client';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { BsThreeDotsVertical, BsDownload } from 'react-icons/bs';
import {
  TbEdit,
  TbNotes,
  TbRotateClockwise2,
  TbFileLike,
} from 'react-icons/tb';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import {
  CheckboxTickIcon,
  ChevronDownIcon,
  DollarIcon,
  DollartimeIcon,
  EarningIcon,
  StarIcon,
  StarsIcon,
} from '@/icons';
import { CiDollar } from 'react-icons/ci';

import Driveractivitytable from './Driveractivitytable';
import Supportlog from './Supportlog';
import DocumentTab from './Documenttab';
import EarningTable from './EarningTable';
import DeleteModal from '@/components/ui/modal/deletemodal';
import DisableDriver from './Disabledrive';
import Input from '@/components/form/input/InputField';
import Select from '@/components/form/Select';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  deleteDriver,
  disableDriverQuery,
  downloadReport,
  editDriverDetails,
  getSpecificDriverDetails,
} from '../state/queries';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast, ToastContainer } from 'react-toastify';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { DriverDetailsType } from '@/utils/types';
import { formatDate } from '@/utils/commonFunction';

// Constants
const MODAL_YEAR_OPTIONS = Array.from({ length: 2026 - 2000 }, (_, i) => ({
  value: `${2000 + i}`,
  label: `${2000 + i}`,
}));

const VEHICLE_OPTIONS = [
  { value: 'sedan', label: 'Sedan' },
  { value: 'van', label: 'Van' },
  { value: 'mini-van', label: 'Mini Van' },
  { value: 'suv', label: 'SUV' },
];

const SHIFT_OPTIONS = [
  { label: 'Evening', value: 'evening' },
  { label: 'Morning', value: 'morning' },
];

const INSURANCE_REMINDER_OPTIONS = [
  { label: 'ON', value: 'on' },
  { label: 'OFF', value: 'off' },
];

const DRIVER_FIELDS = [
  { label: 'Email Id', name: 'email', type: 'email' },
  { label: 'Mobile Number', name: 'contactNo', type: 'tel' },
  { label: 'Current Address', name: 'address', type: 'text' },
  { label: 'Permanent Address', name: 'permanentAddress', type: 'text' },
  { label: 'City', name: 'city', type: 'text' },
  { label: 'State', name: 'state', type: 'text' },
  { label: 'Zip Code', name: 'zipCode', type: 'text' },
  { label: 'Date Of Birth', name: 'dob', type: 'date' },
  { label: 'National ID/SSN', name: 'nationalIdSsn', type: 'text' },
  { label: 'Emergency Contact', name: 'emergencyContact', type: 'tel' },
  {
    label: 'Emerg. Contact Person',
    name: 'emergencyContactPerson',
    type: 'text',
  },
  {
    label: 'Emg. Contact Relation',
    name: 'emergencyContactRelation',
    type: 'text',
  },
];
const stats = [
  {
    icon: <DollarIcon />,
    amount: '€8,031.00',
    label: 'Total Earnings',
    bg: 'bg-[#D7FFEF]',
  },
  {
    icon: <EarningIcon />,
    amount: '€8,031.00',
    label: 'Company Earnings',
    bg: 'bg-[#FFF3E7]',
  },
  {
    icon: <DollartimeIcon />,
    amount: '€8,031.00',
    label: 'Pending Payment',
    bg: 'bg-[#FFEEEE]',
  },
  {
    icon: <StarsIcon />,
    amount: '€8,031.00',
    label: 'Bonuses Earned',
    bg: 'bg-[#D7FFFC]',
  },
  {
    icon: <CheckboxTickIcon />,
    amount: '€8,031.00',
    label: 'Total Trips Completed',
    bg: 'bg-[#EFEEFF]',
  },
];

// Utility Functions
const getDriverStatusClass = (status: string): string => {
  const statusClasses: Record<string, string> = {
    Active: 'text-[#13BB76]',
    Inactive: 'text-[#8F8CD6]',
    Suspended: 'text-[#FF4032]',
    Enroute: 'text-[#1E90FF]',
  };
  return statusClasses[status] || 'text-[#FF8C00]';
};

export default function DriverDetails() {
  // Router and params
  const router = useRouter();
  const params = useParams();
  const driverId = params?.driverdetails as string;
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // States
  const [activeTab, setActiveTab] = useState(0);
  const [deleteProfile, setDeleteProfile] = useState(false);
  const [disableDriver, setDisableDriver] = useState(false);
  const [isEditProfile, setIsEditProfile] = useState(false);
  const [driverDetails, setDriverDetails] = useState<
    DriverDetailsType | undefined
  >();

  const [tippyVisible, setTippyVisible] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState('');

  const [formData, setFormData] = useState<Partial<DriverDetailsType>>({
    fullName: '',
    email: '',
    gender: '',
    dob: '',
    nationalIdSsn: '',
    vehicleAvailability: '',
    model: '',
    plateNumber: '',
    vehicleType: '',
    address: '',
    countryCode: '+1',
    contactNo: '9876543210',
    shift: 'morning',
    region: 'North',
    rating: '4.5',
    city: '',
    state: '',
    zipCode: '',
    color: '',
    lastActive: '',
    driverStatus: '',
    insuranceExpiryDate: '2030-12-31',
    insuranceRenewalReminder: true,
    vehicleRegistration: 'true',
    vehicleDetails: 'vehicleDetails',
    registrationExpiryDate: '',
    registrationStatus: false,
    image: null,
  });

  const [newFormData, setNewFormData] = useState<Record<string, string>>({});

  // React Query for driver data
  const { data: driverData } = useQuery({
    queryKey: ['driverDetails', driverId],
    queryFn: async () => getSpecificDriverDetails(driverId),
    enabled: !!driverId,
  });

  // Mutation Hooks
  const deleteDriverMutation = useMutation({
    mutationFn: (data: string) => deleteDriver(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      setDeleteProfile(false);
      toast.success('Driver Deleted Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
      router.push('/admin/driver');
    },
    onError: err => console.error(err),
  });

  const disableDriverMutation = useMutation({
    mutationFn: async (data: any) => disableDriverQuery(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverDetails'] });
      setDisableDriver(false);
      toast.success('Driver Disabled Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => console.error(err),
  });

  const downloadDriverMutation = useMutation({
    mutationFn: () => downloadReport(driverId),
    onSuccess: async res => {
      const url = window.URL.createObjectURL(
        new Blob([res], { type: 'text/csv' })
      );
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'data.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    onError: err => console.error('Download error', err),
  });

  const editDriverMutation = useMutation({
    mutationFn: async (data: FormData) => editDriverDetails(data, driverId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverDetails'] });
      setIsEditProfile(false);
      toast.success('Profile Edited successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => console.error(err),
  });

  // Event Handlers with useCallback for optimization
  const handleDeleteDriver = useCallback(() => {
    if (driverId) {
      deleteDriverMutation.mutate(driverId);
    }
  }, [driverId, deleteDriverMutation]);

  const handleDisableDriver = useCallback(() => {
    if (driverId) {
      disableDriverMutation.mutate({ driverId, status: 'inactive' });
    }
  }, [driverId, disableDriverMutation]);

  const handleDownloadReport = useCallback(() => {
    downloadDriverMutation.mutate();
  }, [downloadDriverMutation]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setNewFormData(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleFormSubmit = useCallback(() => {
    if (newFormData) {
      editDriverMutation.mutate(newFormData);
    }
  }, [newFormData, editDriverMutation]);

  const handleDateChange = useCallback(
    (date: Date | null, fieldName: string) => {
      setFormData(prev => ({
        ...prev,
        [fieldName]: date ? date.toISOString().split('T')[0] : '',
      }));
    },
    []
  );

  const handleImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files;

      console.log(file[0], 'file');
      if (file) {
        setFormData(prev => ({ ...prev, image: file }));
        setNewFormData(prev => ({ ...prev, image: file[0] }));

        const reader = new FileReader();
        reader.onloadend = () => setImageUrl(reader.result as string);
        reader.readAsDataURL(file?.[0]);
      }
    },
    []
  );

  const handleSendNotification = useCallback(() => {
    toast.success('Notification sent!', {
      autoClose: 5000,
      position: 'top-center',
    });
  }, []);

  const handleSelectChange = useCallback((fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));
    setNewFormData(prev => ({ ...prev, [fieldName]: value }));
  }, []);

  const handleInsuranceReminderChange = useCallback((value: string) => {
    setFormData(prev => ({
      ...prev,
      insuranceRenewalReminder: value === 'on',
    }));

    setNewFormData(prev => ({
      ...prev,
      insuranceRenewalReminder: value === 'on',
    }));
  }, []);

  // Memoized computed values
  const defaultProfileImage =
    'https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg';

  const profileImageSrc = useMemo(() => {
    return imageUrl || driverDetails?.image || defaultProfileImage;
  }, [imageUrl, driverDetails?.image]);

  // Tippy dropdown options with useCallback
  const getTippyOptions = useCallback(() => {
    const commonButtonClass =
      'w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]';

    const createButton = (
      text: string,
      onClick?: () => void,
      children?: React.ReactNode
    ) => (
      <button
        key={text}
        className={commonButtonClass}
        onClick={() => {
          setTippyVisible(false);
          onClick?.();
        }}
      >
        {children || text}
      </button>
    );

    const tabOptions: Record<number, React.ReactNode[]> = {
      0: [
        <DisableDriver
          key="disable-driver"
          isOpen={disableDriver}
          setPropTippyVisible={setTippyVisible}
          setIsOpen={setDisableDriver}
          handleDisableDriver={handleDisableDriver}
        />,
        createButton('Send Notification', handleSendNotification),
        createButton(
          'Send Message',
          undefined,
          <a
            href={`https://wa.me/${driverDetails?.contactNo}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Send Message
          </a>
        ),
        createButton('Export Driver Report', handleDownloadReport),
        <DeleteModal
          key="delete-modal"
          isOpen={deleteProfile}
          setPropTippyVisible={setTippyVisible}
          setIsOpen={setDeleteProfile}
          handleDeleteDriver={handleDeleteDriver}
          id={driverDetails?.id}
        />,
      ],
      1: [
        createButton('Bulk Resolve Issues'),
        createButton('Approve All Pending Trips'),
        createButton('Export Activity Data'),
        createButton('Report Suspicious Activity'),
        createButton('Generate Activity Report'),
        createButton('Download Receipts of all Trips'),
      ],
      2: [
        createButton('Export Earnings Report'),
        createButton('Adjust Earnings'),
        createButton('Payment History'),
        createButton('Set Payment Schedule'),
      ],
      3: [
        createButton('Approve Selected'),
        createButton('Reject Selected'),
        createButton('Send Reminder'),
        createButton('Download'),
        createButton('Export Data'),
        createButton('Update Expiry Date'),
        createButton('Add Comments'),
        createButton('Send Message'),
        createButton('Delete Documents'),
      ],
      4: [
        createButton('View Details'),
        createButton('Resolve Issue'),
        createButton('Re Open Issue'),
        createButton('Re-Assign to New Admin'),
        createButton('Escalate Issue'),
        createButton('Add Comment'),
        createButton('Download Log'),
        createButton('Delete Entry'),
      ],
    };

    return (
      <div className="bg-white text-gray-900">
        <div className="flex flex-col space-y-1 p-1">
          {tabOptions[activeTab] || []}
        </div>
      </div>
    );
  }, [
    activeTab,
    disableDriver,
    driverDetails,
    deleteProfile,
    handleDisableDriver,
    handleDeleteDriver,
    handleSendNotification,
    handleDownloadReport,
  ]);

  // Effects
  useEffect(() => {
    if (driverData?.data?.[0]) {
      const driver = driverData.data[0];
      setFormData(driver); // Populate formData for editing
      setDriverDetails(driver); // Populate driverDetails for displaying
    }
  }, [driverData]);

  useEffect(() => {
    const isEditing = searchParams.get('edit') === 'true';
    setIsEditProfile(isEditing);
    if (!isEditing && driverDetails) {
      // If we cancel editing, reset form data to original details
      setFormData(driverDetails);
    }
  }, [searchParams, driverDetails]);

  // Render earnings stats (memoized for performance)
  const renderEarningsStats = useMemo(() => {
    if (activeTab !== 2) return null;

    return (
      <>
        {stats.map((stat, index) => (
          <div
            key={index}
            className={index === 0 ? 'border-grey-200 border-l pl-6' : ''}
          >
            <div className="flex flex-col gap-1">
              <div
                className={`flex h-[40px] w-[40px] items-center justify-center rounded-full ${stat.bg}`}
              >
                {stat.icon}
              </div>
              <h2 className="font-regular text-[16px] font-semibold text-gray-800">
                {stat.amount}
              </h2>
              <span className="text-xs text-[#76787A]">{stat.label}</span>
            </div>
          </div>
        ))}
      </>
    );
  }, [activeTab]);

  // Main render
  return (
    <>
      <ToastContainer />
      <div className="space-y-6 rounded-xl border border-gray-200">
        {/* Card Header */}
        <div className="items-top mb-0 flex justify-between rounded-xl rounded-b-none bg-[#ffffff] p-6">
          <div className="flex items-center space-x-4">
            <div className="relative h-24 w-24 sm:h-28 sm:w-28 md:h-32 md:w-32">
              {isEditProfile ? (
                <label
                  htmlFor="profileImageUpload"
                  className="group relative flex h-full w-full cursor-pointer items-center justify-center overflow-visible rounded-full bg-gray-100 shadow-md transition-all duration-200 hover:shadow-lg"
                >
                  <input
                    id="profileImageUpload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                    name="image"
                  />
                  <div className="absolute right-2 bottom-2 z-[1] rounded-full bg-white p-1.5 shadow-md transition-transform duration-200 group-hover:scale-110">
                    <svg
                      className="h-4 w-4 text-gray-700"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3z" />
                      <path d="M16 16h2a2 2 0 002-2v-1" />
                    </svg>
                  </div>
                  <img
                    src={profileImageSrc}
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover transition-transform duration-200 group-hover:scale-105"
                  />
                </label>
              ) : (
                <img
                  src={profileImageSrc}
                  alt="Profile"
                  className="h-full w-full rounded-full object-cover shadow-md"
                />
              )}
            </div>

            <div>
              {isEditProfile ? (
                <Input
                  type="text"
                  placeholder="Full Name"
                  className="mb-3"
                  onChange={handleChange}
                  value={formData.fullName || ''} // FIXED
                  name="fullName"
                />
              ) : (
                <h2 className="font-regular text-[24px] text-[#050013]">
                  {driverDetails?.fullName || '--'}
                </h2>
              )}

              <div className="flex items-center space-x-2">
                <span className="bg-active-status h-2 w-2 rounded-full"></span>
                <p
                  className={`text-[11px] ${getDriverStatusClass(
                    driverDetails?.driverStatus || ''
                  )}`}
                >
                  {driverDetails?.driverStatus || '--'}
                </p>
              </div>
            </div>
          </div>

          {renderEarningsStats}

          <div className="hover-effect flex items-start gap-3">
            {isEditProfile && activeTab === 0 ? (
              <button
                className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none"
                onClick={handleFormSubmit}
              >
                Save
              </button>
            ) : (
              <button
                className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                onClick={() => {
                  if (activeTab === 0) setIsEditProfile(true);
                }}
              >
                <TbEdit size={20} />
              </button>
            )}

            {activeTab !== 2 && (
              <>
                <button
                  className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                  onClick={handleDownloadReport}
                >
                  <BsDownload size={20} />
                </button>
                <div className="relative">
                  <button
                    type="button"
                    className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                    onClick={() => setTippyVisible(!tippyVisible)}
                  >
                    <BsThreeDotsVertical size={20} />
                  </button>

                  {tippyVisible && (
                    <>
                      <div
                        className="fixed inset-0 z-10"
                        onClick={() => setTippyVisible(false)}
                      />
                      <div className="absolute right-0 top-full z-20 mt-2 min-w-48 rounded-md border border-gray-200 bg-white shadow-lg">
                        {getTippyOptions()}
                      </div>
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Tabs */}
        <TabGroup selectedIndex={activeTab} onChange={setActiveTab}>
          <TabList
            as="aside"
            className="flex gap-[18px] border-t border-gray-200 bg-white px-[43px] pt-5"
          >
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbNotes className="h-[24px] w-[24px]" />
              General Info
            </Tab>
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbRotateClockwise2 className="h-[24px] w-[24px]" />
              Activity
            </Tab>
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <CiDollar className="h-[24px] w-[24px]" />
              Earnings
            </Tab>
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbFileLike className="h-[24px] w-[24px]" />
              Documents
            </Tab>
            <Tab className="flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbFileLike className="h-[24px] w-[24px]" />
              Support Log
            </Tab>
          </TabList>

          <TabPanels as="section" className="rounded-b-[12px] bg-[#F6F8FB]">
            <TabPanel className="rounded-bl-[12px] px-[43px] py-[23px]">
              <div className="bg-[#F6F8FB]">
                {/* Driver Details Section */}
                <h3 className="text-md mb-[20px] rounded-2xl bg-white px-[27px] py-[9px] text-gray-500">
                  Driver Details
                </h3>
                <div className="mb-[40px] rounded-lg px-[27px]">
                  <div className="grid grid-cols-5 gap-[20px] text-sm text-gray-700">
                    {DRIVER_FIELDS.map((field, index) => {
                      const value =
                        formData?.[field.name as keyof DriverDetailsType] ?? '';
                      let displayValue = value;

                      if (
                        field.name === 'dob' &&
                        value &&
                        typeof value === 'string'
                      ) {
                        displayValue = value.slice(0, 10);
                      }

                      return (
                        <div key={index}>
                          <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                            {field.label}
                          </span>
                          {isEditProfile ? (
                            <Input
                              type={field.type}
                              placeholder={field.label}
                              className="mb-3"
                              onChange={handleChange}
                              value={displayValue || ''}
                              name={field.name}
                            />
                          ) : (
                            <span className="text-[14px] font-medium text-[#050013]">
                              {displayValue || '--'}
                            </span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Documents and Verification Details Section */}
                <h3 className="text-md mb-[20px] rounded-2xl bg-white px-[27px] py-[9px] text-gray-500">
                  Documents and Verification details
                </h3>
                <div className="mb-[40px] rounded-lg px-[27px]">
                  <div className="grid grid-cols-5 gap-[44px] text-sm text-gray-700">
                    {/* Vehicle Model */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Vehicle Model
                      </span>
                      {isEditProfile ? (
                        <div className="relative">
                          <Select
                            options={MODAL_YEAR_OPTIONS}
                            placeholder="Model"
                            defaultValue={formData?.model}
                            onChange={e => handleSelectChange('model', e)}
                            className="dark:bg-dark-900 w-100"
                          />
                        </div>
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.model || '--'}
                        </span>
                      )}
                    </div>

                    {/* Plate Number */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Plate Number
                      </span>
                      {isEditProfile ? (
                        <Input
                          type="text"
                          placeholder="Plate Number"
                          className="mb-3"
                          onChange={handleChange}
                          value={formData.plateNumber || ''}
                          name="plateNumber"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.plateNumber || '--'}
                        </span>
                      )}
                    </div>

                    {/* Vehicle Type */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Vehicle Type
                      </span>
                      {isEditProfile ? (
                        <div className="relative">
                          <Select
                            options={VEHICLE_OPTIONS}
                            placeholder="Vehicle Type"
                            onChange={e => handleSelectChange('vehicleType', e)}
                            className="dark:bg-dark-900 w-100"
                            defaultValue={formData.vehicleType} // FIXED
                          />
                        </div>
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.vehicleType || '--'}
                        </span>
                      )}
                    </div>

                    {/* Vehicle Color */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Vehicle Color
                      </span>
                      {isEditProfile ? (
                        <Input
                          type="text"
                          placeholder="Vehicle Color"
                          className="mb-3"
                          onChange={handleChange}
                          value={formData.color || ''} // FIXED
                          name="color"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.color || '--'}
                        </span>
                      )}
                    </div>

                    {/* Registration Status */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Registration Status
                      </span>
                      {isEditProfile ? (
                        <Input
                          type="text"
                          placeholder="Registration Status"
                          className="mb-3"
                          onChange={handleChange}
                          value={String(formData.registrationStatus) || ''} // FIXED
                          name="registrationStatus"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.registrationStatus
                            ? 'Active'
                            : 'Inactive'}
                        </span>
                      )}
                    </div>

                    {/* Registration Expiry Date */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Registration Expiry Date
                      </span>
                      {isEditProfile ? (
                        <DatePicker
                          selected={
                            formData?.registrationExpiryDate
                              ? new Date(formData.registrationExpiryDate)
                              : null
                          }
                          onChange={(date: Date | null) =>
                            handleDateChange(date, 'registrationExpiryDate')
                          }
                          className="w-full rounded-lg border border-gray-300 p-[11px] text-gray-800"
                          dateFormat="dd/MM/yyyy"
                          placeholderText="Registration Expiry Date"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.registrationExpiryDate || '--'}
                        </span>
                      )}
                    </div>

                    {/* Insurance Expiry Date */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Insurance Expiry Date
                      </span>
                      {isEditProfile ? (
                        <DatePicker
                          selected={
                            formData?.insuranceExpiryDate
                              ? new Date(formData.insuranceExpiryDate)
                              : null
                          }
                          onChange={(date: Date | null) =>
                            handleDateChange(date, 'insuranceExpiryDate')
                          }
                          className="w-full rounded-lg border border-gray-300 p-[11px] text-gray-800"
                          dateFormat="dd/MM/yyyy"
                          placeholderText="Insurance Expiry Date"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {formatDate(driverDetails?.insuranceExpiryDate) ||
                            '--'}
                        </span>
                      )}
                    </div>

                    {/* Insurance Renewal Reminder */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Insurance Renewal Reminder
                      </span>
                      {isEditProfile ? (
                        <div className="relative">
                          <Select
                            options={INSURANCE_REMINDER_OPTIONS}
                            placeholder="Reminder"
                            onChange={handleInsuranceReminderChange}
                            className="dark:bg-dark-900 w-100"
                            defaultValue={
                              formData.insuranceRenewalReminder ? 'on' : 'off'
                            }
                          />
                        </div>
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.insuranceRenewalReminder
                            ? 'ON'
                            : 'OFF'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Professional Details Section */}
                <h3 className="text-md mb-[20px] rounded-2xl bg-white px-[27px] py-[9px] text-gray-500">
                  Professional Details
                </h3>
                <div className="rounded-lg px-[27px]">
                  <div className="grid grid-cols-5 gap-[44px] text-sm text-gray-700">
                    {/* Assigned Region */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Assigned Region
                      </span>
                      {isEditProfile ? (
                        <Input
                          type="text"
                          placeholder="Assigned Region"
                          className="mb-3"
                          onChange={handleChange}
                          value={formData.region || ''} // FIXED
                          name="region"
                        />
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.region || '--'}
                        </span>
                      )}
                    </div>

                    {/* Assigned Shift */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Assigned Shift
                      </span>
                      {isEditProfile ? (
                        <div className="relative">
                          <Select
                            options={SHIFT_OPTIONS}
                            placeholder="Shift"
                            onChange={e => handleSelectChange('shift', e)}
                            className="dark:bg-dark-900 w-100"
                            defaultValue={formData.shift} // FIXED
                          />
                        </div>
                      ) : (
                        <span className="text-[14px] font-medium text-[#050013]">
                          {driverDetails?.shift || '--'}
                        </span>
                      )}
                    </div>

                    {/* Last Active */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Last Active
                      </span>
                      <span className="text-[14px] font-medium text-[#050013]">
                        {driverDetails?.lastActive || '--'}
                      </span>
                    </div>

                    {/* Trips Today */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Trips Today
                      </span>
                      <span className="text-[14px] font-medium text-[#050013]">
                        {driverDetails?.tripsToday || '--'}
                      </span>
                    </div>

                    {/* Average Rating */}
                    <div>
                      <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
                        Average Rating
                      </span>
                      <p className="flex text-[14px] font-medium text-[#050013]">
                        {driverDetails?.rating || '--'} <StarIcon />
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel>
              <Driveractivitytable driverDetails={driverDetails} />
            </TabPanel>
            <TabPanel>
              <EarningTable driverDetails={driverDetails} />
            </TabPanel>
            <TabPanel>
              <DocumentTab driverDetails={driverDetails} />
            </TabPanel>
            <TabPanel>
              <Supportlog driverDetails={driverDetails} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
    </>
  );
}
