'use client';

import React, { useEffect } from 'react';
import DriverTable from './components/Drivertable';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Drivers() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle('Drivers List');
  }, []);
  return (
    <div className="rounded-lg bg-white p-4 dark:bg-gray-800">
      <DriverTable />
    </div>
  );
}
