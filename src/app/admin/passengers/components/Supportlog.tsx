'use client';
import React, {
  useState,
  useMemo,
  JSXElementConstructor,
  ReactElement,
  ReactNode,
  ReactPortal,
  useCallback,
} from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { useQuery } from '@tanstack/react-query';
import { fetchDriverSupportLogs } from '../state/queries'; // Assuming this function exists

// --- TYPE DEFINITIONS ---

// This interface is updated to match the columns in the image.
// You'll need to ensure your API returns these fields.
interface SupportLog {
  id: number;
  ticket_id: string;
  ride_id: string;
  created_at: string;
  issue_category: string;
  communication: string;
  priority: 'High' | 'Medium' | 'Low';
  assigned_agent: string;
  status: 'Pending' | 'Resolved' | 'Unresolved';
  resolution_date: string | null;
  issue_description: string; // Kept for data model, but not displayed in the table
}

interface DocumentTabProps {
  driverDetails: any;
}

// --- UTILITY FUNCTIONS ---

const formatDateTime = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  // This format matches "16-1-2024 11:30 AM"
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  let hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // The hour '0' should be '12'
  const minutesStr = minutes < 10 ? '0' + minutes : minutes;

  return `${day}-${month}-${year} ${hours}:${minutesStr} ${ampm}`;
};

const getStatusClasses = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'resolved':
      return { dot: 'bg-green-500', text: 'text-green-600' };
    case 'pending':
      return { dot: 'bg-orange-500', text: 'text-orange-600' };
    case 'unresolved':
      return { dot: 'bg-red-500', text: 'text-red-600' };
    default:
      return { dot: 'bg-gray-400', text: 'text-gray-500' };
  }
};

const getPriorityClasses = (priority: string) => {
  switch (priority?.toLowerCase()) {
    case 'high':
      return { dot: 'bg-red-500', text: 'text-red-600' };
    case 'medium':
      return { dot: 'bg-orange-500', text: 'text-orange-600' };
    case 'low':
      // Matched the image which shows a yellow-ish dot for Low priority
      return { dot: 'bg-yellow-500', text: 'text-yellow-600' };
    default:
      return { dot: 'bg-gray-400', text: 'text-gray-500' };
  }
};

// --- HELPER COMPONENT ---

const SortIcon = ({
  direction,
  active,
}: {
  direction: 'asc' | 'desc';
  active: boolean;
}) => {
  let icon = active ? (direction === 'asc' ? '↑' : '↓') : '⇅';
  return (
    <span
      className={`ml-1 inline text-[13px] ${
        active ? 'text-gray-700' : 'text-gray-400'
      } transition-colors hover:text-gray-600`}
    >
      {icon}
    </span>
  );
};

// --- MAIN COMPONENT ---

export default function SupportLog({ driverDetails }: DocumentTabProps) {
  const driverId = driverDetails?.id;
  const [sortConfig, setSortConfig] = useState<{
    key: keyof SupportLog;
    direction: 'asc' | 'desc';
  } | null>({ key: 'created_at', direction: 'desc' }); // Default sort by creation date

  const {
    data: supportLogs = [],
    isLoading,
    isError,
    error,
  } = useQuery<SupportLog[], Error>({
    queryKey: ['driverSupportLogs', driverId],
    queryFn: () => fetchDriverSupportLogs(driverId.toString()),
    enabled: !!driverId,
  });

  const handleSort = useCallback((key: keyof SupportLog) => {
    setSortConfig(prevConfig => {
      const isSameKey = prevConfig?.key === key;
      const direction =
        isSameKey && prevConfig.direction === 'asc' ? 'desc' : 'asc';
      return { key, direction };
    });
  }, []);

  const sortedLogs = useMemo(() => {
    if (!sortConfig) return supportLogs;

    return [...supportLogs].sort((a, b) => {
      const valA = a[sortConfig.key];
      const valB = b[sortConfig.key];

      if (valA == null) return 1;
      if (valB == null) return -1;

      if (['created_at', 'resolution_date'].includes(sortConfig.key)) {
        return sortConfig.direction === 'asc'
          ? new Date(valA).getTime() - new Date(valB).getTime()
          : new Date(valB).getTime() - new Date(valA).getTime();
      }

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortConfig.direction === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }
      return 0;
    });
  }, [supportLogs, sortConfig]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="border-l-4 border-red-500 bg-red-50 p-4">
        <p className="text-sm text-red-700">{error.message}</p>
      </div>
    );
  }

  if (driverDetails.length === 0) {
    return (
      <div className="border-l-4 border-blue-500 bg-blue-50 p-4">
        <p className="text-sm text-blue-700">
          No support logs found for this driver.
        </p>
      </div>
    );
  }

  const renderHeaderCell = (label: string, sortKey: keyof SupportLog) => (
    <TableCell
      isHeader
      className="cursor-pointer select-none px-4 py-3 text-start text-[12px] font-medium text-slate-500"
    >
      <div
        onClick={() => handleSort(sortKey)}
        className="flex items-center"
        role="button"
        tabIndex={0}
      >
        {label}
        <SortIcon
          active={sortConfig?.key === sortKey}
          direction={sortConfig?.direction || 'asc'}
        />
      </div>
    </TableCell>
  );

  return (
    <div className="">
      <div className="rounded-b-[12px] border-t-0 bg-white">
        <div className="custom-scrollbar overflow-x-auto ">
          <Table className="overflow-x-auto w-full">
            <TableHeader className="bg-slate-50">
              <TableRow className="border-t-0">
                {renderHeaderCell('Ticket ID', 'ticket_id')}
                {renderHeaderCell('Ride ID', 'ride_id')}
                {renderHeaderCell('Creation Date/Time', 'created_at')}
                {renderHeaderCell('Issue Category', 'issue_category')}
                {renderHeaderCell('Communication', 'communication')}
                {renderHeaderCell('Priority Level', 'priority')}
                {renderHeaderCell('Assigned Agent', 'assigned_agent')}
                {renderHeaderCell('Ticket Status', 'status')}
                {renderHeaderCell('Resolution Date/Time', 'resolution_date')}
              </TableRow>
            </TableHeader>

            <TableBody className="divide-y divide-gray-200">
              {driverDetails?.map(log => (
                <TableRow key={log.id} className="hover:bg-gray-50">
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] font-medium text-gray-800">
                    #{log.ticket_id}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    #{log.ride_id}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    {formatDateTime(log.created_at)}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    {log.issue_category}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    {log.communication}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <span
                        className={`h-2 w-2 rounded-full ${
                          getPriorityClasses(log.priority).dot
                        }`}
                      ></span>
                      <span
                        className={`text-[13px] font-medium ${
                          getPriorityClasses(log.priority).text
                        }`}
                      >
                        {log.priority}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    {log.assigned_agent}
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <span
                        className={`h-2 w-2 rounded-full ${
                          getStatusClasses(log.status).dot
                        }`}
                      ></span>
                      <span
                        className={`text-[13px] font-medium ${
                          getStatusClasses(log.status).text
                        }`}
                      >
                        {log.status}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-4 py-3 whitespace-nowrap text-[13px] text-gray-600">
                    {formatDateTime(log.resolution_date)}
                  </TableCell>
                  <TableCell className="text-gray-500 px-4">
                    <Tippy
                      content={
                        <div className="bg-white text-gray-900 p-1">
                          <button className="w-full rounded px-2 py-1 text-start text-sm text-gray-700 hover:bg-gray-100">
                            View Details
                          </button>
                          <button className="w-full rounded px-2 py-1 text-start text-sm text-gray-700 hover:bg-gray-100">
                            {log.status === 'Resolved'
                              ? 'Reopen Issue'
                              : 'Resolve Issue'}
                          </button>
                        </div>
                      }
                      interactive={true}
                      placement="bottom-end"
                      theme="light"
                      arrow={false}
                      duration={0}
                      className="rounded-lg border border-gray-200 !bg-white shadow-lg"
                    >
                      <button
                        type="button"
                        className="focus:outline-none p-1 rounded-full hover:bg-gray-100"
                      >
                        <BsThreeDotsVertical />
                      </button>
                    </Tippy>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
