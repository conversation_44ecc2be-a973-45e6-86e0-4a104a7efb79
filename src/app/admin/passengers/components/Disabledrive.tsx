import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

type DeleteModalprops = {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setPropTippyVisible: React.Dispatch<React.SetStateAction<boolean>>;
  handleDisableDriver?: () => void;
};

export default function DisableDriver(props: DeleteModalprops) {
  const { isOpen, setIsOpen, handleDisableDriver, setPropTippyVisible } = props;
  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setPropTippyVisible(false);
    setIsOpen(true);
  }
  return (
    <>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-999" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="modal-bg-black fixed inset-0 opacity-[70%]" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg leading-6 font-medium text-[#050013]"
                  >
                    Disable Driver Profile
                  </Dialog.Title>
                  <div className="mt-2">
                    <p className="text-sm text-[#76787A]">
                      Are you sure you want to disable this driver profile? This
                      action cannot be undone.
                    </p>
                  </div>

                  <div className="mt-4 flex items-center justify-end">
                    <button
                      type="button"
                      className="btn-border me-[10px] mb-2 flex items-center gap-2 rounded-full border border-gray-200 bg-white px-5 py-2.5 text-sm font-medium text-blue-700 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                      onClick={closeModal}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                      onClick={handleDisableDriver}
                    >
                      Disable
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
