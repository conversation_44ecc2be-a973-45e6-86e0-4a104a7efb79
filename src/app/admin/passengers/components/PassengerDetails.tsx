'use client';
import React, { useEffect, useState } from 'react';
import { BsThreeDotsVertical, BsDownload } from 'react-icons/bs';
import {
  TbEdit,
  TbNotes,
  TbRotateClockwise2,
  TbFileLike,
} from 'react-icons/tb';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getPassengerBuId,
  editPassengerDetails,
  disableDriverQuery,
  dummySupportLogs, // Assuming this is for a different component
} from '../state/queries';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast, ToastContainer } from 'react-toastify';
import Input from '@/components/form/input/InputField';
import SupportLog from './Supportlog';
import PassengerActivityTable from './PassengerActivitytTable';
import DisableDriver from './Disabledrive';

// Imports for DatePicker
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { subYears } from 'date-fns';

// Reusable component for displaying static detail fields
const DetailField = ({ label, value }: { label: string; value: string }) => (
  <div>
    <span className="mb-2 block text-[13px] whitespace-nowrap text-gray-500">
      {label}
    </span>
    <span className="text-[14px] font-medium text-[#050013]">
      {value || '--'}
    </span>
  </div>
);

export default function PassengerDetails() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const id = params?.passengerdetails as string;

  const [activeTab, setActiveTab] = useState(0);
  const [isEditProfile, setIsEditProfile] = useState(false);
  const [tippyVisible, setTippyVisible] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [disablePassenger, setDisablePassenger] = useState(false);

  const [passengerData, setPassengerData] = useState<any>({});
  const [updatedPassengerData, setUpdatedPassengerData] = useState({});

  const { data: fetchedPassengerData } = useQuery({
    queryKey: ['getPassengerById', id],
    queryFn: () => getPassengerBuId(id),
    enabled: !!id,
  });

  const disablePassengerMutation = useMutation({
    mutationFn: disableDriverQuery,
    onSuccess: () => {
      toast.success('Passenger status updated.', {
        autoClose: 5000,
        position: 'top-center',
      });
      queryClient.invalidateQueries({ queryKey: ['passengerData'] });
    },
    onError: () => toast.error('Failed to update passenger status.'),
  });

  useEffect(() => {
    if (fetchedPassengerData) {
      setPassengerData(fetchedPassengerData.data);
      if (fetchedPassengerData.image) {
        setImageUrl(fetchedPassengerData.image);
      }
    }
  }, [fetchedPassengerData]);

  useEffect(() => {
    setIsEditProfile(searchParams.get('edit') === 'true');
  }, [searchParams]);

  const editPassengerMutation = useMutation({
    mutationFn: (data: any) => editPassengerDetails(data, id),
    onSuccess: res => {
      console.log(res);
      queryClient.invalidateQueries({ queryKey: ['getPassengerById', id] });
      setIsEditProfile(false);
      toast.success('Profile Edited successfully');
    },
    onError: err => {
      console.error(err);
      toast.error('Failed to edit profile');
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassengerData({ ...passengerData, [e.target.name]: e.target.value });
    setUpdatedPassengerData({
      ...updatedPassengerData,
      [e.target.name]: e.target.value,
    });
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toISOString().split('T')[0];
      setPassengerData({ ...passengerData, dateOfBirth: formattedDate });
      setUpdatedPassengerData({
        ...passengerData,
        dateOfBirth: formattedDate,
      });
    } else {
      setPassengerData({ ...passengerData, dateOfBirth: null });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPassengerData((prev: any) => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onloadend = () => setImageUrl(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const handleFormSubmit = () => {
    editPassengerMutation.mutate(updatedPassengerData);
  };

  const getStatusClass = (status: string) => {
    if (status === 'active') return 'text-[#13BB76]';
    if (status === 'inactive') return 'text-[#8F8CD6]';
    return 'text-gray-500';
  };

  const getTippyOptions = () => {
    const buttonClass =
      'w-full rounded px-2 py-1.5 text-start text-sm text-gray-700 hover:bg-gray-100';

    switch (activeTab) {
      // Case 0: General Info Tab
      case 0:
        return (
          <div className="p-1">
            {/* <button
              className={buttonClass}
              onClick={() => {
                setTippyVisible(false);
              }}
            >
              Disable Passenger
            </button> */}
            <button
              className={buttonClass}
              onClick={() => {
                toast.success('Notification sent!', {
                  autoClose: 5000,
                  position: 'top-center',
                });
              }}
            >
              Send Notification
            </button>
            <a
              href={`https://wa.me/${passengerData.contactNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Send Message
            </a>
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Export Report
            </button>
          </div>
        );

      // Case 1: Activity Tab
      case 1:
        return (
          <div className="p-1">
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Export Activity Data
            </button>
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Generate Activity Report
            </button>
          </div>
        );

      // Case 2: Support Log Tab
      case 2:
        return (
          <div className="p-1">
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Add New Log
            </button>
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Export All Logs
            </button>
            <button
              className={buttonClass}
              onClick={() => setTippyVisible(false)}
            >
              Download All Logs
            </button>
          </div>
        );

      // Default case if no tab is matched
      default:
        return <></>;
    }
  };
  return (
    <>
      <ToastContainer />
      <div className="space-y-6 rounded-xl border border-gray-200 w-full">
        {/* Card Header */}
        <div className="items-top mb-0 flex justify-between rounded-xl rounded-b-none bg-[#ffffff] p-6">
          <div className="flex items-center space-x-4">
            <div className="relative h-24 w-24 sm:h-28 sm:w-28 md:h-32 md:w-32">
              {isEditProfile ? (
                <label
                  htmlFor="profileImageUpload"
                  className="group relative flex h-full w-full cursor-pointer items-center justify-center overflow-visible rounded-full bg-gray-100 shadow-md transition-all duration-200 hover:shadow-lg"
                >
                  <input
                    id="profileImageUpload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                    readOnly
                  />
                  <div className="absolute right-2 bottom-2 z-[1] rounded-full bg-white p-1.5 shadow-md transition-transform duration-200 group-hover:scale-110">
                    <svg
                      className="h-4 w-4 text-gray-700"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3z" />
                      <path d="M16 16h2a2 2 0 002-2v-1" />
                    </svg>
                  </div>
                  <img
                    src={
                      imageUrl ||
                      'https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg'
                    }
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover transition-transform duration-200 group-hover:scale-105"
                  />
                </label>
              ) : (
                <img
                  src={
                    imageUrl ||
                    'https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg'
                  }
                  alt="Profile"
                  className="h-full w-full rounded-full object-cover shadow-md"
                />
              )}
            </div>
            <div>
              {isEditProfile ? (
                <Input
                  type="text"
                  placeholder="Full Name"
                  className="mb-3"
                  onChange={handleChange}
                  name="fullName"
                  value={passengerData.fullName || ''}
                />
              ) : (
                <h2 className="font-regular text-[24px] text-[#050013]">
                  {passengerData.fullName || '...'}
                </h2>
              )}

              <div className="flex items-center space-x-2">
                <span
                  className={`h-2 w-2 rounded-full ${
                    passengerData.status === 'active'
                      ? 'bg-green-500'
                      : 'bg-gray-400'
                  }`}
                ></span>
                <p
                  className={`text-[11px] capitalize ${getStatusClass(
                    passengerData.status
                  )}`}
                >
                  {passengerData.status || '...'}
                </p>
              </div>
            </div>
          </div>

          <div className="hover-effect flex items-start gap-3">
            {isEditProfile && activeTab === 0 ? (
              <button
                className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300"
                onClick={handleFormSubmit}
              >
                Save
              </button>
            ) : (
              <button
                className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                onClick={() => {
                  if (activeTab === 0) setIsEditProfile(true);
                }}
              >
                <TbEdit size={20} />
              </button>
            )}
            <button className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200">
              <BsDownload size={20} />
            </button>
            <div className="relative">
              <button
                type="button"
                className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                onClick={() => setTippyVisible(!tippyVisible)}
              >
                <BsThreeDotsVertical size={20} />
              </button>
              {tippyVisible && (
                <>
                  <div
                    className="fixed inset-0 z-10"
                    onClick={() => setTippyVisible(false)}
                  />
                  <div className="absolute right-0 top-full z-20 mt-2 min-w-48 rounded-md border border-gray-200 bg-white shadow-lg">
                    {getTippyOptions()}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        <TabGroup selectedIndex={activeTab} onChange={setActiveTab}>
          <TabList
            as="aside"
            className="flex gap-[18px] border-t border-gray-200 bg-white px-[43px] pt-5"
          >
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbNotes className="h-[24px] w-[24px]" />
              General Info
            </Tab>
            <Tab className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbRotateClockwise2 className="h-[24px] w-[24px]" />
              Activity
            </Tab>
            <Tab className="flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]">
              <TbFileLike className="h-[24px] w-[24px]" />
              Support Log
            </Tab>
          </TabList>
          <TabPanels as="section" className="rounded-b-[12px] bg-[#F6F8FB]">
            <TabPanel className="rounded-bl-[12px] px-[43px] py-[23px]">
              <div className="bg-[#F6F8FB]">
                <h3 className="text-md mb-[20px] rounded-2xl bg-white px-[27px] py-[9px] text-gray-500">
                  Passenger Details
                </h3>
                <div className="mb-[40px] rounded-lg px-[27px]">
                  <div className="grid grid-cols-5 gap-[20px] text-sm text-gray-700">
                    {isEditProfile ? (
                      <>
                        <Input
                          type="text"
                          name="emailId"
                          value={passengerData.emailId}
                          onChange={handleChange}
                        />
                        <Input
                          type="text"
                          name="contactNumber"
                          value={passengerData.contactNumber}
                          onChange={handleChange}
                        />
                        <Input
                          type="text"
                          name="currentAddress"
                          value={passengerData.currentAddress}
                          onChange={handleChange}
                        />
                        <div>
                          <DatePicker
                            selected={
                              passengerData.dateOfBirth
                                ? new Date(passengerData.dateOfBirth)
                                : null
                            }
                            onChange={handleDateChange}
                            dateFormat="yyyy-MM-dd"
                            className="mb-3 p-3 w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholderText="Select Date"
                            showYearDropdown
                            scrollableYearDropdown
                            yearDropdownItemNumber={80}
                            maxDate={subYears(new Date(), 18)}
                          />
                        </div>
                        <Input
                          type="text"
                          name="gender"
                          value={passengerData.gender}
                          onChange={handleChange}
                        />
                        <Input
                          type="text"
                          name="emergencyContactName"
                          value={passengerData.emergencyContactName}
                          onChange={handleChange}
                        />
                        <Input
                          type="text"
                          name="emergencyContactNumber"
                          value={passengerData.emergencyContactNumber}
                          onChange={handleChange}
                        />
                        <Input
                          type="text"
                          name="preferredPaymentMethod"
                          value={passengerData.preferredPaymentMethod}
                          onChange={handleChange}
                        />
                      </>
                    ) : (
                      <>
                        <DetailField
                          label="Email Id"
                          value={passengerData.emailId}
                        />
                        <DetailField
                          label="Mobile Number"
                          value={passengerData.contactNumber}
                        />
                        <DetailField
                          label="Current Address"
                          value={passengerData.currentAddress}
                        />
                        <DetailField
                          label="Date of Birth"
                          value={passengerData.dateOfBirth}
                        />
                        <DetailField
                          label="Gender"
                          value={passengerData.gender}
                        />
                        <DetailField
                          label="Emergency Contact Name"
                          value={passengerData.emergencyContactName}
                        />
                        <DetailField
                          label="Emergency Contact Number"
                          value={passengerData.emergencyContactNumber}
                        />
                        <DetailField
                          label="Preferred Payment"
                          value={passengerData.preferredPaymentMethod}
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel>
              <PassengerActivityTable driverDetails={passengerData} />
            </TabPanel>
            <TabPanel>
              <SupportLog driverDetails={dummySupportLogs} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>

      <DisableDriver
        isOpen={disablePassenger}
        setIsOpen={setDisablePassenger}
        handleDisableDriver={() => {
          //  disablePassengerMutation.mutate({ id:, status:'inactive'});
        }}
        setPropTippyVisible={setTippyVisible}
      />
    </>
  );
}
