'use client';

import React, { useEffect } from 'react';
import PassengersTable from './components/Passengerstable';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Drivers() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Passenger List');
  }, []);
  return (
    <div className="rounded-lg bg-white p-4 dark:bg-gray-800">
      <PassengersTable />
    </div>
  );
}
