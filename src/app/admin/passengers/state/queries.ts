import { backendApiClient } from '@/utils/apiClient';
export async function getTablePassengerDetails() {
  const rawResponse = await backendApiClient
    .get('passengers')
    .json()
    .then(response => response);
  return rawResponse.data;
}

export async function addTablePassengersDetails(payload: any) {
  const rawResponse = await backendApiClient
    .post('passengers', { json: payload })
    .json();
  return rawResponse;
}
export async function getSpecificPassengersDetails(id: string) {
  const rawResponse: any = await backendApiClient
    .get(`passengers/${id}`)
    .json()
    .then(response => response);
  return rawResponse;
}

export const deletePassengers = (payload: string) =>
  backendApiClient.delete(`passengers/${payload}`).json();

export const disableDriverQuery = (payload: any) =>
  backendApiClient
    .patch(`passengers/${payload?.id}/status`, {
      json: { status: payload?.status },
    })
    .json();

// export const downloadReport = (payload: string) =>
//   backendApiClient
//     .get(`driver-management/driver/export-driver/csv/${payload}`)
//     .blob();

export const editPassengerDetails = (payload: FormData, id: string) => {
  console.log(payload, 'payload', id);
  return backendApiClient
    .patch(`passengers/${id}`, {
      json: payload,
    })
    .json();
};

export function getPassengerBuId(id: string) {
  return backendApiClient.get(`passengers/${id}`).json();
}

// This interface should match the one in your SupportLog component
interface SupportLog {
  id: number;
  ticket_id: string;
  ride_id: string;
  created_at: string;
  issue_category: string;
  communication: string;
  priority: 'High' | 'Medium' | 'Low';
  assigned_agent: string;
  status: 'Pending' | 'Resolved' | 'Unresolved';
  resolution_date: string | null;
  issue_description: string;
}

export const dummySupportLogs: SupportLog[] = [
  {
    id: 1,
    ticket_id: '1001',
    ride_id: 'R2020',
    created_at: '2025-09-15T11:30:00Z',
    issue_category: 'Driver Misconduct',
    communication: 'Email',
    priority: 'Low',
    assigned_agent: 'Judalon Menendez',
    status: 'Pending',
    resolution_date: null,
    issue_description:
      'The driver was reported for playing loud music after being asked to lower it.',
  },
  {
    id: 2,
    ticket_id: '1002',
    ride_id: 'R2021',
    created_at: '2025-09-15T11:30:00Z',
    issue_category: 'Delays or No-shows',
    communication: 'Phone call',
    priority: 'Low',
    assigned_agent: 'Michael Johnson',
    status: 'Resolved',
    resolution_date: '2025-09-16T11:30:00Z',
    issue_description:
      'Driver was 15 minutes late for pickup. A refund for the waiting fee was issued.',
  },
  {
    id: 3,
    ticket_id: '1003',
    ride_id: 'R2022',
    created_at: '2025-09-15T11:30:00Z',
    issue_category: 'Delays or No-shows',
    communication: 'Email',
    priority: 'High',
    assigned_agent: 'Jessica Williams',
    status: 'Unresolved',
    resolution_date: null,
    issue_description:
      'Driver did not show up for a scheduled airport pickup. Passenger missed their flight.',
  },
  {
    id: 4,
    ticket_id: '1004',
    ride_id: 'R2023',
    created_at: '2025-09-15T11:30:00Z',
    issue_category: 'Driver Misconduct',
    communication: 'Phone call',
    priority: 'Low',
    assigned_agent: 'Christopher Smith',
    status: 'Resolved',
    resolution_date: '2025-09-16T11:12:00Z',
    issue_description:
      'Passenger reported that the driver took an unnecessarily long route.',
  },
  {
    id: 5,
    ticket_id: '1005',
    ride_id: 'R2024',
    created_at: '2025-09-15T11:30:00Z',
    issue_category: 'Fare Discrepancy',
    communication: 'Email',
    priority: 'Medium',
    assigned_agent: 'Ashley Brown',
    status: 'Pending',
    resolution_date: null,
    issue_description:
      'The final charge was $15 higher than the upfront fare estimate.',
  },
  {
    id: 6,
    ticket_id: '1006',
    ride_id: 'R2025',
    created_at: '2025-09-14T11:30:00Z',
    issue_category: 'Delays or No-shows',
    communication: 'Phone call',
    priority: 'Low',
    assigned_agent: 'Daniel Miller',
    status: 'Resolved',
    resolution_date: '2025-09-14T11:34:00Z',
    issue_description:
      'Minor delay due to traffic, passenger was notified and satisfied.',
  },
  {
    id: 7,
    ticket_id: '1007',
    ride_id: 'R2026',
    created_at: '2025-09-14T11:30:00Z',
    issue_category: 'Vehicle Condition',
    communication: 'Email',
    priority: 'High',
    assigned_agent: 'Emily Davis',
    status: 'Unresolved',
    resolution_date: null,
    issue_description:
      'The vehicle had a strong unpleasant odor and the check engine light was on.',
  },
  {
    id: 8,
    ticket_id: '1008',
    ride_id: 'R2027',
    created_at: '2025-09-14T11:30:00Z',
    issue_category: 'Delays or No-shows',
    communication: 'Phone call',
    priority: 'Medium',
    assigned_agent: 'Matthew Wilson',
    status: 'Resolved',
    resolution_date: '2025-09-14T11:50:00Z',
    issue_description:
      'Driver cancelled the trip last minute without a valid reason.',
  },
  {
    id: 9,
    ticket_id: '1009',
    ride_id: 'R2028',
    created_at: '2025-09-13T11:30:00Z',
    issue_category: 'Driver Misconduct',
    communication: 'Email',
    priority: 'Low',
    assigned_agent: 'Jessica Williams',
    status: 'Pending',
    resolution_date: null,
    issue_description:
      'Driver was talking on the phone for the entire duration of the trip.',
  },
  {
    id: 10,
    ticket_id: '1010',
    ride_id: 'R2029',
    created_at: '2025-09-13T11:30:00Z',
    issue_category: 'Fare Discrepancy',
    communication: 'Phone call',
    priority: 'Low',
    assigned_agent: 'Ashley Brown',
    status: 'Resolved',
    resolution_date: '2025-09-13T11:21:00Z',
    issue_description:
      'A toll fee was incorrectly applied to the fare. The fee has been refunded.',
  },
  {
    id: 11,
    ticket_id: '1011',
    ride_id: 'R2030',
    created_at: '2025-09-12T11:30:00Z',
    issue_category: 'Lost Item',
    communication: 'Email',
    priority: 'High',
    assigned_agent: 'Judalon Menendez',
    status: 'Unresolved',
    resolution_date: null,
    issue_description:
      'Passenger left a laptop in the vehicle, and the driver is unresponsive.',
  },
  {
    id: 12,
    ticket_id: '1012',
    ride_id: 'R2031',
    created_at: '2025-09-12T11:30:00Z',
    issue_category: 'Driver Misconduct',
    communication: 'Phone call',
    priority: 'Medium',
    assigned_agent: 'Matthew Wilson',
    status: 'Resolved',
    resolution_date: '2025-09-12T11:30:00Z',
    issue_description:
      'Complaint about unsafe driving, including speeding. Driver has been issued a warning.',
  },
];
export function fetchPassengerSupportLogs(id: string) {
  return dummySupportLogs;
  return backendApiClient.get(`passengers/${id}/support-logs`).json();
}
