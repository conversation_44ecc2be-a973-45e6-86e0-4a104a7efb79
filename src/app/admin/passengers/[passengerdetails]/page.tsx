'use client';

import React, { useEffect } from 'react';
import PassengerDetails from '../components/PassengerDetails';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Page() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle('Passenger Details');
  }, []);
  return (
    <div className="px-[45px] py-[31px]">
      <PassengerDetails />
    </div>
  );
}
